"""
Configuration settings for the Accent Recognition API
"""
import torch


class Config:
    """Application configuration"""
    
    # Model configuration
    MODEL_SOURCE = "Jzuluaga/accent-id-commonaccent_xlsr-en-english"
    PYMODULE_FILE = "custom_interface.py"
    CLASSNAME = "CustomEncoderWav2vec2Classifier"
    
    # Performance configuration
    CACHE_SIZE = 100  # Number of results to cache
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    # Queue configuration
    MAX_CONCURRENT_REQUESTS = 5  # Maximum concurrent inference requests
    MAX_QUEUE_SIZE = 20  # Maximum requests in queue
    REQUEST_TIMEOUT = 30  # Timeout per request in seconds
    
    # Cleanup configuration
    CLEANUP_INTERVAL = 300  # Cleanup every 5 minutes (300 seconds)
    UPLOADS_FOLDER = "uploads"  # Folder to clean
    MAX_FILE_AGE_HOURS = 1  # Delete files older than 1 hour
    ENABLE_CLEANUP = True  # Enable/disable cleanup system
    
    # CORS configuration
    ALLOWED_ORIGINS = ["http://localhost:5173"]
    
    # API configuration
    API_TITLE = "Accent Recognition API"
    API_VERSION = "2.1.0"


# Global config instance
config = Config()

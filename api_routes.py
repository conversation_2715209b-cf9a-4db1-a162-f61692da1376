"""
API routes module for FastAPI endpoints
"""
import torch
import gc
from fastapi import File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from config import config
from request_processor import request_processor
from cleanup_utils import cleanup_manager
from model_manager import model_manager


async def identify_accent(file: UploadFile = File(...)):
    """Identify accent from uploaded audio file"""
    try:
        # Validate file type
        if not file.filename.lower().endswith(".wav"):
            raise HTTPException(status_code=400, detail="Only .wav files are allowed")

        # Validate file size
        file_content = await file.read()
        if len(file_content) > config.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="File too large")

        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="Empty file")

        # Process the request
        result = await request_processor.process_request(file_content)
        return JSONResponse(content=result)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")

    finally:
        # Force garbage collection to free memory
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()


async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": model_manager.classifier is not None,
        "device": config.DEVICE,
        "torch_version": torch.__version__
    }


async def get_performance_stats():
    """Get performance statistics including queue and cleanup info"""
    stats = request_processor.get_stats()
    cleanup_stats = cleanup_manager.get_stats()
    stats.update(cleanup_stats)
    return stats


async def get_queue_status():
    """Get detailed queue status"""
    return request_processor.get_queue_status()


async def clear_cache():
    """Clear the result cache"""
    cache_size = request_processor.clear_cache()
    
    # Clear model cache
    cache_info = model_manager.clear_cache()

    # Force garbage collection
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

    return {
        "message": f"Cache cleared. Removed {cache_size} entries.",
        "cache_info": cache_info
    }


async def manual_cleanup():
    """Manually trigger file cleanup"""
    return cleanup_manager.manual_cleanup()


async def get_cleanup_status():
    """Get cleanup system status"""
    return cleanup_manager.get_cleanup_status()


async def root():
    """Root endpoint with API information"""
    return {
        "message": "Accent Recognition API",
        "version": config.API_VERSION,
        "response_format": {
            "identify_endpoint": {
                "description": "Returns only US accent confidence",
                "example": {"us_confidence": 85.67},
                "note": "Returns 0.0 if US accent not detected"
            }
        },
        "optimizations": [
            "In-memory file processing (NO disk storage)",
            "Result caching",
            "Model compilation (if supported)",
            "GPU acceleration (if available)",
            "Performance monitoring",
            "Request queuing system",
            "Concurrent request limiting",
            "Automatic file cleanup system"
        ],
        "queue_system": {
            "max_concurrent_requests": config.MAX_CONCURRENT_REQUESTS,
            "max_queue_size": config.MAX_QUEUE_SIZE,
            "request_timeout": config.REQUEST_TIMEOUT
        },
        "endpoints": {
            "/identify": "POST - Get US accent confidence from audio file",
            "/health": "GET - Health check",
            "/stats": "GET - Performance statistics with queue and cleanup info",
            "/queue-status": "GET - Detailed queue status",
            "/cleanup-status": "GET - File cleanup system status",
            "/clear-cache": "POST - Clear cache",
            "/cleanup-files": "POST - Manually trigger file cleanup",
            "/": "GET - API information"
        }
    }

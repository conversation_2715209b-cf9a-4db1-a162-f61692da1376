"""
Main FastAPI application for Accent Recognition API
Refactored into modular components for better maintainability
"""
import warnings
import sys
import logging
import time
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
import starlette.middleware.base as middleware_base

# Import our modules
from config import config
from model_manager import model_manager
from request_processor import request_processor
from cleanup_utils import cleanup_manager
import api_routes

# Setup clean logging configuration
warnings.filterwarnings("ignore", category=UserWarning, module="speechbrain")
warnings.filterwarnings("ignore", category=UserWarning, module="torchaudio")
warnings.filterwarnings("ignore", category=UserWarning, module="torchvision")
warnings.filterwarnings("ignore", category=FutureWarning, module="huggingface_hub")
warnings.filterwarnings("ignore", message=".*torchaudio._backend.set_audio_backend has been deprecated.*")
warnings.filterwarnings("ignore", message=".*Failed to load image Python extension.*")
warnings.filterwarnings("ignore", message=".*resume_download.*deprecated.*")
warnings.filterwarnings("ignore", message=".*Some weights of Wav2Vec2Model were not initialized.*")
warnings.filterwarnings("ignore", message=".*You should probably TRAIN this model.*")

# Setup minimal logging - only show errors and critical info
logging.basicConfig(
    level=logging.WARNING,  # Only show warnings and errors
    format='%(levelname)s: %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

# Suppress all verbose loggers
logging.getLogger("speechbrain").setLevel(logging.ERROR)
logging.getLogger("transformers").setLevel(logging.ERROR)
logging.getLogger("huggingface_hub").setLevel(logging.ERROR)
logging.getLogger("torch").setLevel(logging.ERROR)
logging.getLogger("torchaudio").setLevel(logging.ERROR)
logging.getLogger("uvicorn").setLevel(logging.WARNING)
logging.getLogger("uvicorn.access").setLevel(logging.ERROR)  # Disable access logs

logger = logging.getLogger(__name__)

# Initialize FastAPI
app = FastAPI(title=config.API_TITLE)


# Request logging middleware
class RequestLoggingMiddleware(middleware_base.BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Log incoming request
        start_time = time.time()
        timestamp = time.strftime("%H:%M:%S")

        # Get client IP
        client_ip = request.client.host if request.client else "unknown"

        # Log request
        if request.url.path == "/identify":
            print(f"[{timestamp}] REQUEST POST {request.url.path} from {client_ip}")
        elif request.url.path == "/health":
            print(f"[{timestamp}] HEALTH GET {request.url.path} from {client_ip}")
        elif request.url.path == "/docs" or request.url.path == "/openapi.json":
            print(f"[{timestamp}] DOCS GET {request.url.path} from {client_ip}")
        elif request.url.path == "/":
            print(f"[{timestamp}] ROOT GET {request.url.path} from {client_ip}")
        elif not request.url.path.startswith("/static"):  # Skip static files
            print(f"[{timestamp}] API {request.method} {request.url.path} from {client_ip}")

        # Process request
        response = await call_next(request)

        # Log response
        process_time = time.time() - start_time
        timestamp = time.strftime("%H:%M:%S")

        if request.url.path == "/identify":
            if response.status_code == 200:
                print(f"[{timestamp}] RESPONSE: {response.status_code} OK ({process_time:.3f}s)")
            else:
                print(f"[{timestamp}] RESPONSE: {response.status_code} ERROR ({process_time:.3f}s)")
        elif not request.url.path.startswith("/static") and request.url.path not in ["/docs", "/openapi.json"]:
            if response.status_code == 200:
                print(f"[{timestamp}] RESPONSE: {response.status_code} OK ({process_time:.3f}s)")
            else:
                print(f"[{timestamp}] RESPONSE: {response.status_code} ERROR ({process_time:.3f}s)")

        return response


# Add middleware
app.add_middleware(RequestLoggingMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=config.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Load model at startup
model_manager.load_model()


@app.on_event("startup")
async def startup_event():
    """Start background tasks"""
    # Start request processor
    await request_processor.start_queue_processor()
    
    # Start cleanup task
    await cleanup_manager.start_cleanup_task()


@app.on_event("shutdown")
async def shutdown_event():
    """Clean shutdown"""
    # Stop request processor
    await request_processor.stop_queue_processor()
    
    # Stop cleanup task
    await cleanup_manager.stop_cleanup_task()


# Register API routes
app.post("/identify")(api_routes.identify_accent)
app.get("/health")(api_routes.health_check)
app.get("/stats")(api_routes.get_performance_stats)
app.get("/queue-status")(api_routes.get_queue_status)
app.post("/clear-cache")(api_routes.clear_cache)
app.post("/cleanup-files")(api_routes.manual_cleanup)
app.get("/cleanup-status")(api_routes.get_cleanup_status)
app.get("/")(api_routes.root)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

"""
Model management module for accent recognition
Handles model loading, device management, and inference
"""
import torch
import torchaudio
import time
import io
from speechbrain.pretrained.interfaces import foreign_class
from functools import lru_cache
from config import config


class ModelManager:
    """Manages the accent recognition model"""
    
    def __init__(self):
        self.classifier = None
        
    def ensure_device_consistency(self):
        """Ensure all model components are on the correct device"""
        try:
            # Move main classifier
            if hasattr(self.classifier, 'to'):
                self.classifier.to(config.DEVICE)

            # Move model components
            if hasattr(self.classifier, 'mods'):
                for name, module in self.classifier.mods.named_children():
                    if hasattr(module, 'to'):
                        module.to(config.DEVICE)

                    # Handle wav2vec2 model specifically
                    if name == 'wav2vec2' and hasattr(module, 'model'):
                        wav2vec_model = module.model
                        if hasattr(wav2vec_model, 'to'):
                            wav2vec_model.to(config.DEVICE)

                        # Move internal components
                        for component_name in ['feature_extractor', 'feature_projection', 'encoder']:
                            if hasattr(wav2vec_model, component_name):
                                getattr(wav2vec_model, component_name).to(config.DEVICE)

                        # Force move parameters and buffers
                        for param in wav2vec_model.parameters():
                            if param.device.type != config.DEVICE:
                                param.data = param.data.to(config.DEVICE)

                        for buffer in wav2vec_model.buffers():
                            if buffer.device.type != config.DEVICE:
                                buffer.data = buffer.data.to(config.DEVICE)

                    # Move other submodules
                    for submodule in module.children():
                        if hasattr(submodule, 'to'):
                            submodule.to(config.DEVICE)

            # Move hparams components
            if hasattr(self.classifier, 'hparams'):
                if hasattr(self.classifier.hparams, 'softmax'):
                    if hasattr(self.classifier.hparams.softmax, 'to'):
                        self.classifier.hparams.softmax.to(config.DEVICE)

                if hasattr(self.classifier.hparams, 'label_encoder'):
                    if hasattr(self.classifier.hparams.label_encoder, 'to'):
                        self.classifier.hparams.label_encoder.to(config.DEVICE)

            # Force move remaining parameters and buffers
            for param in self.classifier.parameters():
                if param.device.type != config.DEVICE:
                    param.data = param.data.to(config.DEVICE)

            for buffer in self.classifier.buffers():
                if buffer.device.type != config.DEVICE:
                    buffer.data = buffer.data.to(config.DEVICE)

        except Exception:
            pass  # Silently handle device consistency errors

    def load_model(self):
        """Load and optimize the accent recognition model"""
        print("Loading accent recognition model...")
        start_time = time.time()

        try:
            # Load model with suppressed output
            import contextlib
            from io import StringIO

            with contextlib.redirect_stdout(StringIO()), contextlib.redirect_stderr(StringIO()):
                self.classifier = foreign_class(
                    source=config.MODEL_SOURCE,
                    pymodule_file=config.PYMODULE_FILE,
                    classname=config.CLASSNAME
                )

            # Move model to appropriate device
            if hasattr(self.classifier, 'to'):
                self.classifier = self.classifier.to(config.DEVICE)

            # Ensure all model components are on the correct device
            if hasattr(self.classifier, 'mods'):
                for name, module in self.classifier.mods.named_children():
                    if hasattr(module, 'to'):
                        module.to(config.DEVICE)

            # Enable optimizations for PyTorch 2.0+
            if hasattr(torch, 'compile') and torch.__version__ >= "2.0":
                try:
                    if hasattr(self.classifier, 'mods'):
                        if hasattr(self.classifier.mods, 'wav2vec2'):
                            self.classifier.mods.wav2vec2 = torch.compile(self.classifier.mods.wav2vec2)
                        if hasattr(self.classifier.mods, 'output_mlp'):
                            self.classifier.mods.output_mlp = torch.compile(self.classifier.mods.output_mlp)
                except Exception:
                    pass

            # Set model to evaluation mode
            self.classifier.eval()

            # Test model warmup
            warmup_success = False
            for attempt in range(2):  # Try CUDA first, then CPU
                try:
                    current_device = config.DEVICE if attempt == 0 else "cpu"
                    
                    if attempt == 1:
                        config.DEVICE = "cpu"
                        self.ensure_device_consistency()

                    dummy_audio = torch.randn(1, 16000).to(current_device)
                    with torch.no_grad():
                        _ = self.classifier.encode_batch(dummy_audio)

                    warmup_success = True
                    break

                except Exception as e:
                    error_msg = str(e).lower()
                    if attempt == 0 and ("device" in error_msg or "cuda" in error_msg):
                        continue  # Try CPU mode
                    elif attempt == 1:
                        break

            if not warmup_success:
                raise RuntimeError("Model warmup failed")

            load_time = time.time() - start_time
            print(f"Model loaded successfully on {config.DEVICE.upper()} ({load_time:.2f}s)")

        except Exception as e:
            print(f"Model loading failed: {e}")
            raise RuntimeError(f"Model loading failed: {e}")

    @lru_cache(maxsize=config.CACHE_SIZE)
    def cached_inference(self, file_hash: str, file_content_bytes: bytes) -> float:
        """Cached inference function - returns only US confidence"""
        try:
            # Convert bytes back to audio tensor
            audio_buffer = io.BytesIO(file_content_bytes)

            # Load audio directly from memory
            waveform, sample_rate = torchaudio.load(audio_buffer, format="wav")

            # Ensure correct sample rate (16kHz for wav2vec2)
            if sample_rate != 16000:
                resampler = torchaudio.transforms.Resample(sample_rate, 16000)
                waveform = resampler(waveform)

            # Ensure waveform is on the correct device
            waveform = waveform.to(config.DEVICE)

            # Perform inference
            with torch.no_grad():
                # Use encode_batch and output_mlp directly for better performance
                outputs = self.classifier.encode_batch(waveform)

                # Ensure outputs are on the correct device before passing to output_mlp
                if hasattr(outputs, 'to'):
                    outputs = outputs.to(config.DEVICE)

                outputs = self.classifier.mods.output_mlp(outputs).squeeze(1)
                out_prob = self.classifier.hparams.softmax(outputs)

                # Extract US confidence directly
                us_confidence = self.get_us_confidence_from_prob(out_prob)

            return us_confidence

        except Exception as e:
            raise RuntimeError(f"Inference failed: {str(e)}")

    def get_us_confidence_from_prob(self, out_prob: torch.Tensor) -> float:
        """Extract US confidence score from probabilities"""
        try:
            # Ensure tensor is on CPU for label processing
            out_prob_cpu = out_prob.cpu() if out_prob.is_cuda else out_prob
            
            # Handle batch dimension - take first element if batch
            if len(out_prob_cpu.shape) > 1:
                out_prob_cpu = out_prob_cpu[0]  # Take first batch element

            # Create indices tensor on the same device as the model
            indices = torch.arange(len(out_prob_cpu))
            
            # Use hparams.label_encoder instead of label_encoder directly
            labels = self.classifier.hparams.label_encoder.decode_ndim(indices)

            us_index = None
            for i, label in enumerate(labels):
                if label.lower() == "us":
                    us_index = i
                    break

            if us_index is not None:
                us_confidence = float(out_prob_cpu[us_index].item())  # Use .item() for scalar conversion
                return round(us_confidence * 100, 2)  # Convert to percentage
            return 0.0  # Return 0 if US not found
        except Exception:
            return 0.0

    def clear_cache(self):
        """Clear the inference cache"""
        self.cached_inference.cache_clear()
        return self.cached_inference.cache_info()._asdict()


# Global model manager instance
model_manager = ModelManager()

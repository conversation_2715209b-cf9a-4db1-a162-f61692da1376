# Accent Recognition API - Modular Structure

## Overview
The application has been refactored from a single large `main.py` file (866 lines) into a modular structure for better maintainability, readability, and organization.

## File Structure

```
av-backend/
├── main.py                 # Main FastAPI application (120 lines)
├── config.py              # Configuration settings (35 lines)
├── model_manager.py       # Model loading and inference (200 lines)
├── request_processor.py   # Request queuing and processing (180 lines)
├── cleanup_utils.py       # File cleanup utilities (120 lines)
├── api_routes.py          # API endpoint handlers (100 lines)
├── main_backup.py         # Backup of original main.py
└── README_MODULAR.md      # This documentation
```

## Module Descriptions

### 1. `main.py` (120 lines)
- **Purpose**: Main FastAPI application entry point
- **Contains**: 
  - FastAPI app initialization
  - Middleware setup
  - Request logging
  - Startup/shutdown events
  - Route registration
- **Dependencies**: All other modules

### 2. `config.py` (35 lines)
- **Purpose**: Centralized configuration management
- **Contains**:
  - Model configuration (source, device, etc.)
  - Performance settings (cache size, timeouts)
  - Queue configuration
  - Cleanup settings
  - CORS and API settings
- **Dependencies**: torch

### 3. `model_manager.py` (200 lines)
- **Purpose**: Model loading, device management, and inference
- **Contains**:
  - ModelManager class
  - Device consistency handling
  - Model loading and optimization
  - Cached inference function
  - US confidence extraction
- **Dependencies**: torch, torchaudio, speechbrain, config

### 4. `request_processor.py` (180 lines)
- **Purpose**: Request queuing, processing, and performance monitoring
- **Contains**:
  - RequestProcessor class
  - QueuedRequest dataclass
  - PerformanceMonitor class
  - Async request processing
  - Cache management
- **Dependencies**: asyncio, config, model_manager

### 5. `cleanup_utils.py` (120 lines)
- **Purpose**: File cleanup and maintenance tasks
- **Contains**:
  - CleanupManager class
  - Periodic cleanup tasks
  - Manual cleanup triggers
  - Cleanup statistics
- **Dependencies**: asyncio, pathlib, config

### 6. `api_routes.py` (100 lines)
- **Purpose**: FastAPI route handlers
- **Contains**:
  - All API endpoint functions
  - Request validation
  - Response formatting
  - Error handling
- **Dependencies**: fastapi, all other modules

## Benefits of Modular Structure

### ✅ **Maintainability**
- Each module has a single responsibility
- Easier to locate and fix bugs
- Simpler to add new features

### ✅ **Readability**
- Reduced file sizes (35-200 lines vs 866 lines)
- Clear separation of concerns
- Better code organization

### ✅ **Testability**
- Each module can be tested independently
- Easier to mock dependencies
- Better unit test coverage

### ✅ **Reusability**
- Modules can be imported and used separately
- Configuration can be easily changed
- Components can be swapped out

### ✅ **Scalability**
- Easy to add new modules
- Simple to extend existing functionality
- Better team collaboration

## Running the Application

The application runs exactly the same as before:

```bash
# Activate virtual environment
venv\scripts\activate

# Run the server
uvicorn main:app --host 0.0.0.0 --port 8000 --log-level warning
```

## API Endpoints

All endpoints remain the same:
- `POST /identify` - Accent recognition
- `GET /health` - Health check
- `GET /stats` - Performance statistics
- `GET /queue-status` - Queue status
- `POST /clear-cache` - Clear cache
- `POST /cleanup-files` - Manual cleanup
- `GET /cleanup-status` - Cleanup status
- `GET /` - API information
- `GET /docs` - API documentation

## Migration Notes

- **Backup**: Original `main.py` is saved as `main_backup.py`
- **Compatibility**: All functionality remains identical
- **Performance**: No performance impact, only organizational changes
- **Dependencies**: No new dependencies added

## Development Workflow

1. **Configuration changes**: Edit `config.py`
2. **Model changes**: Edit `model_manager.py`
3. **API changes**: Edit `api_routes.py`
4. **Queue/processing changes**: Edit `request_processor.py`
5. **Cleanup changes**: Edit `cleanup_utils.py`
6. **App structure changes**: Edit `main.py`

## Testing

Test the refactored application:

```bash
# Health check
curl http://localhost:8000/health

# API documentation
open http://localhost:8000/docs

# Performance stats
curl http://localhost:8000/stats
```

All functionality should work exactly as before, but now with much better code organization! 🎉

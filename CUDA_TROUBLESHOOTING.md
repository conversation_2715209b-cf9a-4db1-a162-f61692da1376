# 🔧 CUDA/GPU Troubleshooting Guide

## ⚠️ Common Error: CUDA/CPU Mismatch

### Error Message:
```
Inference error: Input type (torch.FloatTensor) and weight type (torch.cuda.FloatTensor) should be the same or input should be a MKLDNN tensor and weight is a dense tensor
```

### 🔍 Root Cause:
This error occurs when there's a mismatch between:
- Model weights are on GPU (CUDA) but input tensors are on CPU
- Model weights are on CPU but input tensors are on GPU
- Different parts of the model are on different devices

### ✅ Solutions Applied in Code:

#### 1. **Device Consistency Check**
The code now includes automatic device consistency checks:
```python
def ensure_model_device_consistency():
    """Ensure all model components are on the correct device"""
    if hasattr(classifier, 'to'):
        classifier.to(config.DEVICE)
    
    if hasattr(classifier, 'mods'):
        for name, module in classifier.mods.named_children():
            if hasattr(module, 'to'):
                module.to(config.DEVICE)
```

#### 2. **Proper Tensor Device Handling**
All tensors are explicitly moved to the correct device:
```python
# Ensure waveform is on the correct device
waveform = waveform.to(config.DEVICE)

# Ensure outputs are on the correct device
if hasattr(outputs, 'to'):
    outputs = outputs.to(config.DEVICE)
```

#### 3. **Model Warmup with Device Check**
The model is warmed up during startup to catch device issues early:
```python
try:
    dummy_audio = torch.randn(1, 16000).to(config.DEVICE)
    with torch.no_grad():
        _ = classifier.encode_batch(dummy_audio)
    print("Model warmup successful")
except Exception as e:
    if "should be the same" in str(e).lower():
        print("Attempting to fix device mismatch...")
        # Auto-fix device issues
```

## 🚀 Installation for GPU Support

### Step 1: Check Your CUDA Version
```bash
nvidia-smi
```

### Step 2: Install Correct PyTorch Version

**For CUDA 11.8 (Most Compatible):**
```bash
pip uninstall torch torchaudio torchvision
pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cu118
```

**For CUDA 12.1 (Latest):**
```bash
pip uninstall torch torchaudio torchvision
pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cu121
```

**For CPU Only:**
```bash
pip uninstall torch torchaudio torchvision
pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --index-url https://download.pytorch.org/whl/cpu
```

### Step 3: Verify Installation
```python
import torch
print(f"CUDA Available: {torch.cuda.is_available()}")
print(f"CUDA Version: {torch.version.cuda}")
print(f"Device Count: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"Current Device: {torch.cuda.current_device()}")
    print(f"Device Name: {torch.cuda.get_device_name()}")
```

## 🔧 Manual Troubleshooting

### If Error Persists:

#### 1. **Force CPU Mode**
Edit `main.py` and change:
```python
class Config:
    DEVICE = "cpu"  # Force CPU mode
```

#### 2. **Clear Model Cache**
```bash
# Remove cached models
rm -rf ~/.cache/huggingface/
rm -rf pretrained_models/
rm -rf wav2vec2_checkpoints/
```

#### 3. **Reinstall Dependencies**
```bash
pip uninstall speechbrain torch torchaudio
pip install -r requirements.txt
```

#### 4. **Check GPU Memory**
```bash
nvidia-smi
```
If GPU memory is full, restart the system or kill GPU processes.

## 📊 GPU Compatibility

| GPU Series | Recommended CUDA | PyTorch Command |
|------------|------------------|-----------------|
| RTX 40xx | CUDA 11.8 or 12.1 | `--index-url https://download.pytorch.org/whl/cu118` |
| RTX 30xx | CUDA 11.8 | `--index-url https://download.pytorch.org/whl/cu118` |
| RTX 20xx | CUDA 11.8 | `--index-url https://download.pytorch.org/whl/cu118` |
| GTX 16xx | CUDA 11.8 | `--index-url https://download.pytorch.org/whl/cu118` |
| Older GPUs | CPU Mode | `--index-url https://download.pytorch.org/whl/cpu` |

## 🎯 Performance Tips

### GPU Mode (Recommended):
- **Faster inference** (2-5x speedup)
- **Better for production** with multiple requests
- **Requires CUDA-compatible GPU**

### CPU Mode (Fallback):
- **Works on any system**
- **Slower inference** but still functional
- **Good for development/testing**

## 🔍 Debugging Commands

### Check Current Setup:
```bash
python -c "import torch; print('CUDA:', torch.cuda.is_available()); print('Device:', torch.cuda.get_device_name() if torch.cuda.is_available() else 'CPU')"
```

### Test Model Loading:
```bash
python -c "from main import load_and_optimize_model; load_and_optimize_model()"
```

### Monitor GPU Usage:
```bash
watch -n 1 nvidia-smi
```

## 📞 Support

If issues persist:
1. Check the error logs in the terminal
2. Verify CUDA installation with `nvidia-smi`
3. Try CPU mode as fallback
4. Check GPU memory usage
5. Restart the system if needed

The code now includes automatic device consistency checks and should handle most CUDA/CPU mismatch issues automatically.

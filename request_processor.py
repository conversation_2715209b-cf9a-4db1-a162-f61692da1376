"""
Request processing module for handling queued requests and caching
"""
import asyncio
import hashlib
import time
import psutil
from asyncio import Semaphore, Queue
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any
from config import config


@dataclass
class QueuedRequest:
    """Data class for queued requests"""
    request_id: str
    file_content: bytes
    timestamp: datetime
    future: asyncio.Future


class PerformanceMonitor:
    """Monitor API performance metrics"""
    
    def __init__(self):
        self.request_count = 0
        self.total_inference_time = 0.0
        self.cache_hits = 0

    def log_request(self, inference_time: float, cache_hit: bool = False):
        """Log a request for performance tracking"""
        self.request_count += 1
        self.total_inference_time += inference_time
        if cache_hit:
            self.cache_hits += 1

    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        avg_time = self.total_inference_time / max(self.request_count, 1)
        cache_hit_rate = self.cache_hits / max(self.request_count, 1) * 100
        return {
            "total_requests": self.request_count,
            "average_inference_time": round(avg_time, 3),
            "cache_hit_rate": round(cache_hit_rate, 2),
            "memory_usage_mb": round(psutil.Process().memory_info().rss / 1024 / 1024, 2)
        }


class RequestProcessor:
    """Handles request queuing and processing"""
    
    def __init__(self):
        # Request queue system
        self.request_semaphore = Semaphore(config.MAX_CONCURRENT_REQUESTS)
        self.request_queue = Queue(maxsize=config.MAX_QUEUE_SIZE)
        self.active_requests = {}
        self.request_counter = 0
        
        # Result cache
        self.result_cache = {}
        
        # Performance monitoring
        self.monitor = PerformanceMonitor()
        
        # Background tasks
        self.queue_processor_task = None

    def calculate_file_hash(self, file_content: bytes) -> str:
        """Calculate MD5 hash of file content for caching"""
        return hashlib.md5(file_content).hexdigest()

    async def start_queue_processor(self):
        """Start the background queue processor"""
        self.queue_processor_task = asyncio.create_task(self.process_request_queue())

    async def stop_queue_processor(self):
        """Stop the background queue processor"""
        if self.queue_processor_task:
            self.queue_processor_task.cancel()
            try:
                await self.queue_processor_task
            except asyncio.CancelledError:
                pass

    async def process_request_queue(self):
        """Background task to process queued requests"""
        from model_manager import model_manager
        
        while True:
            try:
                # Get request from queue
                queued_request = await self.request_queue.get()

                # Check if request is still valid (not timed out)
                if queued_request.future.cancelled():
                    self.request_queue.task_done()
                    continue

                # Acquire semaphore for concurrent request limiting
                async with self.request_semaphore:
                    try:
                        # Process the request
                        file_hash = self.calculate_file_hash(queued_request.file_content)

                        # Check cache first
                        if file_hash in self.result_cache:
                            result = self.result_cache[file_hash]
                        else:
                            # Perform inference
                            us_confidence = model_manager.cached_inference(file_hash, queued_request.file_content)
                            result = {"us_confidence": us_confidence}

                            # Cache the result
                            if len(self.result_cache) >= config.CACHE_SIZE:
                                # Remove oldest entry (simple FIFO)
                                oldest_key = next(iter(self.result_cache))
                                del self.result_cache[oldest_key]

                            self.result_cache[file_hash] = result

                        # Set the result
                        if not queued_request.future.cancelled():
                            queued_request.future.set_result(result)

                    except Exception as e:
                        if not queued_request.future.cancelled():
                            queued_request.future.set_exception(e)

                    finally:
                        # Remove from active requests
                        self.active_requests.pop(queued_request.request_id, None)
                        self.request_queue.task_done()

            except Exception:
                await asyncio.sleep(1)

    async def process_request(self, file_content: bytes) -> Dict[str, Any]:
        """Process a request either immediately or through queue"""
        from model_manager import model_manager
        
        start_time = time.time()

        # Calculate file hash for caching
        file_hash = self.calculate_file_hash(file_content)

        # Check cache first
        cache_hit = file_hash in self.result_cache
        if cache_hit:
            result = self.result_cache[file_hash]
            inference_time = time.time() - start_time
            self.monitor.log_request(inference_time, cache_hit=True)
            return result

        # Check if we can process immediately or need to queue
        if self.request_semaphore.locked() or self.request_queue.qsize() > 0:
            # Queue is needed
            if self.request_queue.full():
                raise Exception("Server too busy. Please try again later.")

            # Create queued request
            self.request_counter += 1
            request_id = f"req_{self.request_counter}_{int(time.time())}"
            future = asyncio.Future()

            queued_request = QueuedRequest(
                request_id=request_id,
                file_content=file_content,
                timestamp=datetime.now(),
                future=future
            )

            # Add to queue and active requests
            await self.request_queue.put(queued_request)
            self.active_requests[request_id] = queued_request

            # Wait for result with timeout
            try:
                result = await asyncio.wait_for(future, timeout=config.REQUEST_TIMEOUT)
                inference_time = time.time() - start_time
                self.monitor.log_request(inference_time, cache_hit=False)
                return result

            except asyncio.TimeoutError:
                # Cancel the request
                future.cancel()
                self.active_requests.pop(request_id, None)
                raise Exception("Request timeout")

        else:
            # Process immediately
            async with self.request_semaphore:
                try:
                    us_confidence = model_manager.cached_inference(file_hash, file_content)
                    result = {"us_confidence": us_confidence}

                    # Cache the result
                    if len(self.result_cache) >= config.CACHE_SIZE:
                        # Remove oldest entry (simple FIFO)
                        oldest_key = next(iter(self.result_cache))
                        del self.result_cache[oldest_key]

                    self.result_cache[file_hash] = result

                    inference_time = time.time() - start_time
                    self.monitor.log_request(inference_time, cache_hit=False)
                    return result

                except Exception as e:
                    raise Exception(f"Inference error: {str(e)}")

    def clear_cache(self):
        """Clear the result cache"""
        cache_size = len(self.result_cache)
        self.result_cache.clear()
        return cache_size

    def get_queue_status(self) -> Dict[str, Any]:
        """Get detailed queue status"""
        return {
            "queue_size": self.request_queue.qsize(),
            "max_queue_size": config.MAX_QUEUE_SIZE,
            "active_requests": len(self.active_requests),
            "max_concurrent_requests": config.MAX_CONCURRENT_REQUESTS,
            "available_slots": config.MAX_CONCURRENT_REQUESTS - len(self.active_requests),
            "queue_full": self.request_queue.full(),
            "semaphore_locked": self.request_semaphore.locked(),
            "active_request_ids": list(self.active_requests.keys())
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics"""
        stats = self.monitor.get_stats()
        stats.update({
            "cache_size": len(self.result_cache),
            "max_cache_size": config.CACHE_SIZE,
            "device": config.DEVICE,
            "queue_size": self.request_queue.qsize(),
            "max_queue_size": config.MAX_QUEUE_SIZE,
            "active_requests": len(self.active_requests),
            "max_concurrent_requests": config.MAX_CONCURRENT_REQUESTS,
            "available_slots": config.MAX_CONCURRENT_REQUESTS - len(self.active_requests)
        })
        return stats


# Global request processor instance
request_processor = RequestProcessor()

"""
Cleanup utilities module for file management and maintenance
"""
import asyncio
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any
from config import config


class CleanupManager:
    """Manages file cleanup and maintenance tasks"""
    
    def __init__(self):
        self.cleanup_stats = {
            "last_cleanup": None,
            "total_cleanups": 0,
            "files_deleted": 0,
            "bytes_freed": 0
        }
        self.cleanup_task = None

    def cleanup_old_files(self):
        """Clean up old files from uploads folder and temp directories"""
        if not config.ENABLE_CLEANUP:
            return

        try:
            files_deleted = 0
            bytes_freed = 0
            cutoff_time = datetime.now() - timedelta(hours=config.MAX_FILE_AGE_HOURS)

            # Clean uploads folder
            uploads_path = Path(config.UPLOADS_FOLDER)
            if uploads_path.exists():
                for file_path in uploads_path.glob("*"):
                    if file_path.is_file():
                        file_stat = file_path.stat()
                        file_modified = datetime.fromtimestamp(file_stat.st_mtime)

                        if file_modified < cutoff_time:
                            file_size = file_stat.st_size
                            try:
                                file_path.unlink()
                                files_deleted += 1
                                bytes_freed += file_size
                            except Exception:
                                pass  # Silently ignore deletion errors

            # Clean system temp files related to our app (optional)
            temp_dir = Path(tempfile.gettempdir())
            for temp_file in temp_dir.glob("tmp*"):
                if temp_file.is_file():
                    try:
                        file_stat = temp_file.stat()
                        file_modified = datetime.fromtimestamp(file_stat.st_mtime)

                        if file_modified < cutoff_time:
                            file_size = file_stat.st_size
                            temp_file.unlink()
                            files_deleted += 1
                            bytes_freed += file_size
                    except Exception:
                        # Ignore temp file cleanup errors
                        pass

            # Update cleanup stats
            self.cleanup_stats["last_cleanup"] = datetime.now()
            self.cleanup_stats["total_cleanups"] += 1
            self.cleanup_stats["files_deleted"] += files_deleted
            self.cleanup_stats["bytes_freed"] += bytes_freed

        except Exception:
            pass  # Silently ignore cleanup errors

    async def periodic_cleanup(self):
        """Background task for periodic cleanup"""
        while True:
            try:
                await asyncio.sleep(config.CLEANUP_INTERVAL)
                self.cleanup_old_files()
            except Exception:
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    async def start_cleanup_task(self):
        """Start the periodic cleanup background task"""
        if config.ENABLE_CLEANUP:
            self.cleanup_task = asyncio.create_task(self.periodic_cleanup())
            self.cleanup_old_files()  # Run initial cleanup

    async def stop_cleanup_task(self):
        """Stop the periodic cleanup background task"""
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass

    def manual_cleanup(self) -> Dict[str, Any]:
        """Manually trigger file cleanup and return results"""
        if not config.ENABLE_CLEANUP:
            return {
                "message": "Cleanup system is disabled",
                "cleanup_enabled": False
            }

        files_before = self.cleanup_stats["files_deleted"]
        bytes_before = self.cleanup_stats["bytes_freed"]

        self.cleanup_old_files()

        files_cleaned = self.cleanup_stats["files_deleted"] - files_before
        bytes_cleaned = self.cleanup_stats["bytes_freed"] - bytes_before

        return {
            "message": "Manual cleanup completed",
            "files_deleted": files_cleaned,
            "bytes_freed": bytes_cleaned,
            "cleanup_stats": self.cleanup_stats.copy()
        }

    def get_cleanup_status(self) -> Dict[str, Any]:
        """Get cleanup system status"""
        uploads_path = Path(config.UPLOADS_FOLDER)
        uploads_info = {
            "exists": uploads_path.exists(),
            "file_count": 0,
            "total_size": 0
        }

        if uploads_path.exists():
            files = list(uploads_path.glob("*"))
            uploads_info["file_count"] = len([f for f in files if f.is_file()])
            uploads_info["total_size"] = sum(f.stat().st_size for f in files if f.is_file())

        return {
            "cleanup_enabled": config.ENABLE_CLEANUP,
            "cleanup_interval": config.CLEANUP_INTERVAL,
            "max_file_age_hours": config.MAX_FILE_AGE_HOURS,
            "uploads_folder": config.UPLOADS_FOLDER,
            "uploads_info": uploads_info,
            "cleanup_stats": self.cleanup_stats.copy(),
            "in_memory_processing": True,
            "note": "Files are processed in-memory, no disk storage used for uploads"
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get cleanup statistics"""
        return {
            "cleanup_enabled": config.ENABLE_CLEANUP,
            "cleanup_stats": self.cleanup_stats.copy()
        }


# Global cleanup manager instance
cleanup_manager = CleanupManager()
